import { apiRequest } from "@/utils/apiRequest";
import { decryptData } from "@/utils/setCookies";
import Cookies from "js-cookie";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export const getVendorBusinessCategory = async () => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  const response = await apiRequest({
    url: `${BASE_URL}/vendor/business-category`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const kycVerificationRequest = async (data) => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  console.log("Token:", accessToken);

  // const formData = objectToFormData(data);
  // for (let [key, value] of formData.entries()) {
  //   console.log(key, value);
  // }

  const response = await fetch(`${BASE_URL}/vendor/company-profile`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: data,
  });
  console.log(response);

  return response;
};

export const getSpecifications = async () => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  const response = await apiRequest({
    url: `${BASE_URL}/vendor/specifications`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const submitQuotation = async (data, id) => {
  const accessToken = decryptData(Cookies.get("accessToken"));

  // Validate required parameters
  if (!id) {
    throw new Error("Specification ID is required");
  }

  if (!accessToken) {
    throw new Error("Access token is required");
  }

  console.log("submitQuotation function called with:", data);
  // Create FormData for file uploads
  const formData = new FormData();

  // Add text fields
  formData.append("product_details", data.productDetails);
  formData.append("price", data.price.toString());
  formData.append("quantity", data.quantity.toString());
  formData.append("delivery_date", data.deliveryTimeline);
  formData.append("terms_conditions", data.termsAndConditions);

  // Add certification files
  if (data.certifications && data.certifications.length > 0) {
    data.certifications.forEach((file, index) => {
      formData.append(`product_certifications[${index}]`, file);
    });
  }

  // Add product image files
  if (data.productImages && data.productImages.length > 0) {
    data.productImages.forEach((file, index) => {
      formData.append(`product_images[${index}]`, file);
    });
  }

  // Debug FormData contents
  console.log("FormData contents:");
  for (let [key, value] of formData.entries()) {
    if (value instanceof File) {
      console.log(`${key}:`, {
        name: value.name,
        size: value.size,
        type: value.type,
      });
    } else {
      console.log(`${key}:`, value);
    }
  }

  console.log("Final formData:", formData);

  const response = await apiRequest({
    url: `${BASE_URL}/vendor/specifications/${id}/quotations`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      Accept: "application/json",
    },
    body: formData,
    isFormData: true,
    // redirect: "manual",
  });
  return response;
};
